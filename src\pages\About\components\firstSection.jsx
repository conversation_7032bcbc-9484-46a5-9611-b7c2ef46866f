import "./firstSection.sass";
import aboutRightSide from "../../../assets/img/aboutRightSide.png";
import separator from "../../../assets/img/Separator.png";
import fbIcone from "../../../assets/img/fbIcone.png";
import instaIcone from "../../../assets/img/instaIcone.png";
import xIcone from "../../../assets/img/xIcone.png";
import biggSeparator from "../../../assets/img/biggSeparator.png";
import TransText from "../../../components/TransText";

export const FirstSectionAbout = () => {
    return (
        <>
            <section className='bg-[#F8F3E9]'>
                <div className='bgshop flex justify-center items-center text-2xl font-serif text-white'>
                    <p>About us</p>
                </div>
                <div className='flex justify-center lg:pt-16 lg:pb-48 pb-10'>
                    <div className='w-[90%]  grid lg:grid-cols-2 lg:gap-12 gap-6'>
                        <div className='lg:px-8'>
                            <p className='lg:text-3xl text-2xl font-serif pt-6'>Hijab Store Islamic</p>
                            <p className='lg:text-xl  lg:pt-6 pt-3 lg:pr-6'>The largest Muslim clothing manufacturer in Asia. We have been established since 2004 and have opened branches in various countries!</p>
                            <p className='lg:text-xl  lg:pt-6 pt-3 lg:pr-6'>We started to expand our business in Muslim countries around the world in 2010, and we have been trusted by many clients as the best Muslim clothing manufacturer. We always maintain the quality and design of every product we make.</p>
                        </div>
                        <div className='lg:px-6'>
                            <img src={aboutRightSide} alt="" />
                        </div>
                    </div>
                </div>
                <div className='bg-[#E7D8C7] lg:h-32 py-6 lg:py-0 w-full lg:relative'>
                    <div className="lg:absolute grid lg:grid-cols-3 lg:gap-10 gap-4 lg:-top-24 w-full lg:px-28 px-20">
                        <div className='flex bg-white w-100 lg:py-6 py-2 '>
                            <p className='bg-[#CEA87C] pl-8 pr-4 text-3xl  h-[fit-content] mt-2 py-2 text-white font-serif'>1</p>
                            <div className='flex flex-col justify-center w-full items-center lg:gap-4 gap-2 '>
                                <p className='lg:text-2xl text-xl font-medium'>Total Orders</p>
                                <p className='lg:text-4xl text-2xl font-serif  '><span className='font-light lg:text-5xl text-3xl'>10</span>B+</p>
                            </div>
                        </div>
                        <div className='flex bg-white w-100 lg:py-6 py-2 '>
                            <p className='bg-[#CEA87C] pl-8 pr-4 text-3xl  h-[fit-content] mt-2 py-2 text-white font-serif'>2</p>
                            <div className='flex flex-col justify-center w-full items-center lg:gap-4 gap-2 '>
                                <p className='lg:text-2xl text-xl font-medium'>Active Customers</p>
                                <p className='lg:text-4xl text-2xl font-serif '><span className='font-light lg:text-5xl text-3xl'>10</span>B+</p>
                            </div>
                        </div>
                        <div className='flex bg-white w-100 lg:py-6 py-2 '>
                            <p className='bg-[#CEA87C] pl-8 pr-4 text-3xl  h-[fit-content] mt-2 py-2 text-white font-serif'>3</p>
                            <div className='flex flex-col justify-center w-full items-center lg:gap-4 gap-2 '>
                                <p className='lg:text-2xl text-xl font-medium'>Store Branch</p>
                                <p className='lg:text-4xl text-2xl font-serif  '><span className='font-light lg:text-5xl text-3xl '>10</span>+</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='flex flex-col items-center'>
                    <p className=' lg:pt-24 pb-4 pt-6 lg:text-3xl text-2xl font-serif'> Our Social Media</p>
                    <img src={separator} alt="" className='lg:w-[fit-content] w-[60vw]' />
                    <div className='grid lg:grid-cols-3 lg:gap-20 gap-6 pt-6 lg:pb-20'>
                        <div className='flex gap-6 items-center'>
                            <img src={fbIcone} alt="" />
                            <p className='text-lg font-medium'>HijabStoreIslamic</p>
                        </div>
                        <div className='flex gap-6 items-center'>
                            <img src={instaIcone} alt="" />
                            <p className='text-lg font-medium'>HijabStoreIslamic</p>
                        </div>
                        <div className='flex gap-6 items-center'>
                            <img src={xIcone} alt="" />
                            <p className='text-lg font-medium'>HijabStoreIslamic</p>
                        </div>

                    </div>
                </div>
                <div className='flex flex-col items-center py-10'>
                    <img src={biggSeparator} alt="" className='lg:w-[fit-content] w-[80vw]' />
                </div>

            </section>
        </>
    )
}
