import React, { createContext, useEffect, useState } from "react";

// ^^ pictures :
import bs1 from "../assets/img/bs1.png";
import bs2 from "../assets/img/bs2.png";
import bs3 from "../assets/img/bs3.png";
import bs4 from "../assets/img/bs4.png";
import trending1 from "../assets/img/trending1.png";
import trending2 from "../assets/img/trending2.png";
import trending3 from "../assets/img/trending3.png";
import trending4 from "../assets/img/trending4.png";
import trending5 from "../assets/img/trending5.png";
import trending6 from "../assets/img/trending6.png";
import trending7 from "../assets/img/trending7.png";
import trending8 from "../assets/img/trending8.png";
import newest1 from "../assets/img/newest1.png";
import newest2 from "../assets/img/newest2.png";
import newest3 from "../assets/img/newest3.png";
import newest4 from "../assets/img/newest4.png";
import newest5 from "../assets/img/newest5.png";
import newest6 from "../assets/img/newest6.png";
import blog1 from "../assets/img/blog1.png";
import blog2 from "../assets/img/blog2.png";
import blog3 from "../assets/img/blog3.png";
import blog4 from "../assets/img/blog4.png";
import blog5 from "../assets/img/blog5.png";
import blog6 from "../assets/img/blog6.png";
import blog7 from "../assets/img/blog7.png";
import blog8 from "../assets/img/blog8.png";
import blog9 from "../assets/img/blog9.png";
import axios from "axios";

export const MyContext = createContext();

export const MyProvider = ({ children }) => {
  const url = "https://management.hijabstoreislamic.com/api/";
  const URLIMAGE = "https://management.hijabstoreislamic.com/storage/images/";
  const savedSelectedLanguage = localStorage.getItem("selectedLanguage");
  const [selectedLanguage, setSelectedLanguage] = useState(
    savedSelectedLanguage ?? "fr"
  );
  localStorage.setItem("selectedLanguage", selectedLanguage ?? "fr");

  const [product, setProduct] = useState();
  const [blog, setBlog] = useState();

  const [sidebareBoolean, setSidebareBoolean] = useState(false);
  const [basket, setBasket] = useState([]);
  const [categories, setCategories] = useState();
  const [colors, setColors] = useState();
  const fetchProducts = async () => {
    try {
      const response = await axios.get(url + "products");
      // console.log(" data :", response.data.data);
      setProduct(response.data.data);
    } catch (error) {
      console.log(
        "Error fetching products (check url fetching in app context) :",
        error
      );
    }
  };
  const fetchBlogs = async () => {
    try {
      const response = await axios.get(url + "blogs");
      setBlog(response.data);
      console.log("blogs : ",response.data)
    } catch (error) {
      console.log(
        "Error fetching blogs (check url fetching in app context) :",
        error
      );
    }
  };
  const fetchCategories = async () => {
    try {
      const response = await axios.get(url + "categories");
      setCategories(response.data);
      // console.log("categories :",response.data);
    } catch (error) {
      console.log(
        "Error fetching categories (check url fetching in app context) :",
        error
      );
    }
  };

  const fetchColors = async () => {
    try {
      const response = await axios.get(url + "colors");
      setColors(response.data);
      console.log("colors :", response.data);
    } catch (error) {
      console.log(
        "Error fetching colors (check url fetching in app context) :",
        error
      );
    }
  };

  useEffect(() => {
    fetchProducts();
    fetchBlogs();
    fetchCategories();
    fetchColors();
  }, []);

  const arrayBasket = [...basket];
  const AddToCard = (productId, colorIndex = 0, size = "", quantity = 1) => {
    console.log("Adding to cart:", { productId, colorIndex, size, quantity });

    let productAdd = product.filter((element) => element.id == productId);
    if (productAdd.length === 0) {
      alert("Product not found");
      return;
    }

    // Create a unique identifier for the product variant
    const variantId = `${productId}_${colorIndex}_${size}`;

    let newProduct = {
      ...productAdd[0],
      quantity: quantity,
      selectedColorIndex: colorIndex,
      selectedSize: size,
      variantId: variantId
    };

    // Check if this exact variant already exists in basket
    let questionBasket = basket.filter((element) => element.variantId === variantId);

    if (questionBasket.length == 0) {
      arrayBasket.push(newProduct);
      setBasket(arrayBasket);
    } else {
      let updatedBasket = [...basket];
      let productplus = updatedBasket.filter(
        (element) => element.variantId === variantId
      );
      productplus[0].quantity += quantity;
      setBasket(updatedBasket);
    }
    alert("Product added to cart successfully");
  };

  const removeFromCart = (variantId) => {
    const updatedBasket = basket.filter((item) => item.variantId !== variantId);
    setBasket(updatedBasket);
  };

  return (
    <>
      <MyContext.Provider
        value={[
          product,
          setProduct,
          blog,
          setBlog,
          url,
          URLIMAGE,
          sidebareBoolean,
          setSidebareBoolean,
          basket,
          setBasket,
          AddToCard,
          categories,
          colors,
          selectedLanguage,
          setSelectedLanguage,
          removeFromCart,
        ]}
      >
        {children}
      </MyContext.Provider>
    </>
  );
};
