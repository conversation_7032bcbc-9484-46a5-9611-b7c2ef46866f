import React, { useContext } from "react";
import { MyContext } from "../../utils/ContextProvider";
import "./sidebare.sass";
import { WhatsAppOrderButton } from "../../components/WhatsAppButton";

export const Sidebar = () => {
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);

  const toggleSidebar = () => {
    setSidebareBoolean(!sidebareBoolean);
  };

  const calculTotal = () => {
    let total = 0;
    basket.forEach((element) => {
      total += element.inventories[0].postPrice * element.quantity;
    });
    return total;
  };
  const orderPanel = {
    products: [...basket],
    total: calculTotal(),
  };
  return (
    <div>
      {/* Sidebar Background Overlay */}
      {sidebareBoolean && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggleSidebar} // Clicking the background closes the sidebar
        />
      )}

      {/* Sidebar Content */}
      <div
        className={`fixed top-0 right-0 h-full bg-black text-white p-4 transition-transform z-50 lg:w-[25vw] w-[80vw] 
        ${sidebareBoolean ? "translate-x-0" : "translate-x-full"}`}
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside from closing the sidebar
      >
        <div className="relative h-full flex flex-col ">
          {/* Close Button */}
          <button
            onClick={toggleSidebar}
            className="absolute top-2 right-2 text-white"
          >
            <span className="text-xl font-bold">X</span> 
          </button>

          <h2 className="text-2xl font-bold mb-4">Panier: </h2>
          <hr />
          <div className="flex flex-col gap-1 mt-2 overflow-y-auto lg:max-h-[80vh] max-h-[78vh] lg:text-lg text-sm my-4 custom-scrollbar pr-2">
            {basket.map((element, id) => {
              const selectedColorIndex = element.selectedColorIndex || 0;
              const selectedVariant = element.inventories[0].variants[selectedColorIndex];

              return (
                <div
                  key={element.variantId || id}
                  className="flex gap-4 p-2 border-2 rounded-lg font-bold relative"
                >
                  {/* Delete button */}
                  <button
                    onClick={() => removeFromCart(element.variantId)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                    title="Remove from cart"
                  >
                    ×
                  </button>

                  <img
                    className="w-[25%]"
                    src={URLIMAGE + selectedVariant.images[0]}
                    alt=""
                  />
                  <div className="text-white flex flex-col gap-1 flex-1">
                    <p className="text-sm">{element.name.en}</p>

                    {/* Color and Size info */}
                    <div className="flex gap-2 items-center text-xs">
                      <div className="flex items-center gap-1">
                        <span>Color:</span>
                        <div
                          className="w-3 h-3 rounded-full border border-white"
                          style={{ backgroundColor: selectedVariant.color }}
                        ></div>
                      </div>
                      {element.selectedSize && (
                        <div className="flex items-center gap-1">
                          <span>Size:</span>
                          <span className="bg-white text-black px-1 rounded text-xs">
                            {element.selectedSize}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-between text-xs">
                      <p>{element.inventories[0].postPrice}.00 MAD</p>
                      <p
                        className={
                          element.inventories[0].exPrice === 0
                            ? "hidden"
                            : "line-through"
                        }
                      >
                        {element.inventories[0].exPrice}.00 MAD
                      </p>
                    </div>
                    <p className="text-sm">x {element.quantity}</p>
                  </div>
                </div>
              );
            })}
          </div>
          <hr />
          <div className="absolute bottom-4 flex justify-between w-full ">
            <WhatsAppOrderButton orderPanel={orderPanel} />
            <div
              onClick={() => alert("nqi rak tqdity")}
              className="bg-white text-black lg:py-2 py-1 px-2 rounded-md "
            >
              Total: {calculTotal()} MAD
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
