// Define color variables
$next-prev-color: #F8F3E9  // Red color for navigation buttons

.swiper
    .swiper-button-next,
    .swiper-button-prev
        color: $next-prev-color  // Apply color to both next and prev buttons
        &:hover
          color: darken($next-prev-color, 50%)  // Darker shade on hover (optional)

// .bgLastSection
//     background-image: url('../../../assets/img/bgLastSection.png')
//     background-size: contain
//     background-position: center
//     background-repeat: no-repeat
//     height: 90vh
//     width: 100%
// .bgleftLS
//     background-image: url('../../../assets/img/leftLS.png')
//     background-size: cover
//     background-position: center
//     background-repeat: no-repeat
//     height: 35vh
//     width: 26%
// .bgcenterLS
//     background-image: url('../../../assets/img/centerLS.png')
//     background-size: cover
//     background-position: center
//     background-repeat: no-repeat
//     height: 35vh
//     width: 26%
// .bgrightLS
//     background-image: url('../../../assets/img/rightLS.png')
//     background-size: cover
//     background-position: center
//     background-repeat: no-repeat
//     height: 35vh
//     width: 26%



.bgLastSection
    background-image: url('../../../assets/img/bgLastSection.png')
    background-size: contain
    background-position: center
    background-repeat: no-repeat
    height: 90vh
    width: 100%

.bgleftLS, .bgcenterLS, .bgrightLS
    background-size: cover
    background-position: center
    background-repeat: no-repeat
    height: 35vh
    width: 26%

.bgleftLS
    background-image: url('../../../assets/img/leftLS.png')

.bgcenterLS
    background-image: url('../../../assets/img/centerLS.png')

.bgrightLS
    background-image: url('../../../assets/img/rightLS.png')

// Responsive Design
@media (max-width: 1024px)
    .bgleftLS, .bgcenterLS, .bgrightLS
        width: 30%
        height: fit-content

@media (max-width: 768px)
    .bgleftLS, .bgcenterLS, .bgrightLS
        width: 70%
        height: fit-content

@media (max-width: 480px)
    .bgleftLS, .bgcenterLS, .bgrightLS
        width: 70%
        height: fit-content
    .bgLastSection
        height: fit-content
        width: 100%
        background-size: cover
        background-position: top
        background-repeat: no-repeat

