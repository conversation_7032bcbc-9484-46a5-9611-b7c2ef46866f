import { useContext } from "react";
import "./firstSection.sass";
import { MyContext } from "../../../utils/ContextProvider";
import { Swiper, SwiperSlide } from "swiper/react";

import { Autoplay, Navigation } from "swiper/modules";
import { useNavigate } from "react-router-dom";

import "swiper/css";
import "swiper/css/navigation";

import { IoEyeOutline } from "react-icons/io5";

import image1 from "../../../assets/img/testBlog1.png";
import image2 from "../../../assets/img/testBlog2.jpg";
import carousel1 from "../../../assets/img/carousel1.png";
import separator from "../../../assets/img/Separator.png";
import categoryLeft from "../../../assets/img/categoryLeft.png";
import categorytopleft from "../../../assets/img/categorytopleft.png";
import categorytopright from "../../../assets/img/categorytopright.png";
import categorybottom from "../../../assets/img/categorybottom.png";
import categoryright from "../../../assets/img/categoryright.png";
import biggSeparator from "../../../assets/img/biggSeparator.png";
import leftLS from "../../../assets/img/leftLS.png";
import centerLS from "../../../assets/img/centerLS.png";
import rightLS from "../../../assets/img/rightLS.png";
import picLS from "../../../assets/img/picLS.png";
import {
  WhatsAppButton,
  WhatsAppOrderButton,
} from "../../../components/WhatsAppButton";
import ProductCard from "../../../components/ProductCard";
import Translator from "../../../components/Translator";
// import bgLastSection from "../../../assets/img/bgLastSection.png"
export const FirstSectionHome = () => {
  const navigate = useNavigate();
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);
  const clients = [
    {
      nom: "zakaria Dahar",
      feedback:
        "Our website looks amazing thanks to Innovatechs. They're professional, creative, and easy to work with.",
      image: image1,
    },
    {
      nom: "Hamza Oufkir",
      feedback:
        "We're so happy with our new website! Innovatechs made the process smooth and stress-free.",
      image: image2,
    },
  ];
  // const bestSeller = product?.filter(
  //   (element) => element.status == "bestseller"
  // );
  // const trending = product?.filter((element) => element.status == "trending");
  const calculTotal = () => {
    let total = 0;
    basket.forEach((element) => {
      total += element.inventories[0].postPrice * element.quantity;
    });
    return total;
  };
  const orderPanel = {
    products: [...basket],
    total: calculTotal(),
  };

  return (
    <>
      <section>
        <div>
          <Swiper
            spaceBetween={50}
            slidesPerView={1}
            navigation={true}
            loop={true}
            autoplay={{
              delay: 4000,
              disableOnInteraction: true,
            }}
            modules={[Autoplay, Navigation]}
            //   onSlideChange={() => console.log("slide change")}
            //   onSwiper={(swiper) => console.log(swiper)}
          >
            {clients?.map((element, index) => (
              <SwiperSlide key={index}>
                <div className="lg:h-[90vh] h-[80vh]  w-full flex justify-between  gap-3 border bg-[#D2B298] rounded max-[480px]:relative ">
                  <div className="w-100  text-white  h-[100%] lg:pl-52 pl-16 flex flex-col gap-6 lg:pb-0 pb-6  justify-center max-[480px]:absolute">
                    <p className="lg:text-6xl text-4xl  font-serif ">
                      Discount <span className="font-sans">30%</span>
                    </p>
                    <p className="lg:text-3xl text-xl font-normal">
                      Young Pashmina Brown Soft
                    </p>
                    <div className="flex items-center gap-8">
                      <p className="lg:text-3xl text-xl">150.00 MAD</p>
                      <p className="line-through">200.00 MAD</p>
                    </div>
                    <button
                      onClick={() => {
                        navigate("/shop");
                      }}
                      className="bg-gray-200 text-[#D2B298] w-[fit-content] px-10 py-2 text-xs font-medium"
                    >
                      Buy Now
                    </button>
                  </div>
                  <div key={index} className="flex justify-between w-100">
                    <img className="" src={carousel1} />
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
        <div className="bg-[#F8F3E9] w-full">
          {/* Categories Section */}
          <div className="flex flex-col items-center">
            <p className=" lg:pt-24 lg:pb-4 pb-2 pt-10 lg:text-5xl text-3xl font-serif">
              {" "}
              <Translator en="Categories" fr="Catégories" ar="فئات" />
            </p>
            <img
              src={separator}
              alt=""
              className="lg:w-[fit-content] w-[60vw] "
            />
            <p className="lg:text-2xl text-lg  pb-8 lg:w-[25vw] text-center pt-4 font-normal">
              <Translator
                en="See all products that fall into this category"
                fr="Voir tous les produits de cette catégorie"
                ar="عرض جميع المنتجات التي تندرج تحت هذه الفئة"
              />
            </p>
          </div>
          <div className="flex justify-center ">
            <div className=" flex lg:flex-row flex-col justify-between  gap-8  lg:h-[80vh] w-[80vw]">
              <div className="lg:w-1/4 relative  overflow-hidden ">
                <img
                  src={categoryLeft}
                  alt=""
                  className="w-[100%] lg:h-[100%] h-[50vh]  object-cover object-top"
                />
                <div className="bg-[#d2b298] font-serif bg-opacity-80  flex justify-center  items-center w-full h-[10vh] absolute bottom-0 ">
                  <p className="text-3xl text-black"> chi haja</p>
                </div>
              </div>
              <div className="lg:w-2/4 grid lg:grid-rows-2 gap-y-8 lg:h-[100%]">
                <div className="grid lg:grid-cols-2 gap-8 lg:h-[100%]">
                  <div className="relative ">
                    <img
                      src={categorytopleft}
                      alt=""
                      className="w-[100%] lg:h-[100%] h-[50vh] lg:object-none object-cover object-top"
                    />
                    <div className="bg-[#d2b298] font-serif bg-opacity-80  flex justify-center  items-center w-full h-[10vh] absolute bottom-0 ">
                      <p className="text-3xl text-black"> chi haja</p>
                    </div>
                  </div>
                  <div className="relative ">
                    <img
                      src={categorytopright}
                      alt=""
                      className="w-[100%] lg:h-[100%] h-[50vh] lg:object-none object-cover object-top"
                    />
                    <div className="bg-[#d2b298] font-serif bg-opacity-80  flex justify-center  items-center w-full h-[10vh] absolute bottom-0 ">
                      <p className="text-3xl text-black"> chi haja</p>
                    </div>
                  </div>
                </div>
                <div className="lg:h-[100%] relative ">
                  <img
                    src={categorybottom}
                    alt=""
                    className="w-[100%] lg:h-[100%] h-[50vh] lg:object-none object-cover object-top"
                  />
                  <div className="bg-[#d2b298] font-serif bg-opacity-80  flex justify-center  items-center w-full h-[10vh] absolute bottom-0 ">
                    <p className="text-3xl text-black"> chi haja</p>
                  </div>
                </div>
              </div>
              <div className="lg:w-1/4 relative ">
                <img
                  src={categoryright}
                  alt=""
                  className="w-[100%] lg:h-[100%] h-[50vh]  object-cover object-top"
                />
                <div className="bg-[#d2b298] font-serif bg-opacity-80  flex justify-center  items-center w-full h-[10vh] absolute bottom-0 ">
                  <p className="text-3xl text-black"> chi haja</p>
                </div>
              </div>
            </div>
          </div>
          {/* Best seller section */}
          <div className="flex flex-col items-center">
            <p className=" lg:pt-24 lg:pb-4 pb-2 pt-10 lg:text-5xl text-3xl font-serif">
              {" "}
              <Translator
                en="Best Seller"
                fr="Meilleure vente"
                ar="الأكثر مبيعًا"
              />
            </p>
            <img
              src={separator}
              alt=""
              className="lg:w-[fit-content] w-[60vw] "
            />
            <p className="lg:text-2xl text-lg  pb-8 lg:w-[25vw] text-center pt-4 font-normal ">
              <Translator
                en="Top selling products"
                fr="Produits les plus vendus"
                ar="المنتجات الأكثر مبيعًا"
              />
            </p>
          </div>
          <div className=" flex justify-center  lg:pt-16 lg:pb-10 ">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 lg:gap-16 gap-8 w-[80vw]">
              {product?.map(
                (element, ind) =>
                  ind < 4 && (
                    // <div key={ind} className="flex flex-col lg:gap-4 gap-2">
                    //   <div className="relative h-[60vh]  group overflow-hidden border-b-2 border-[#d2b298]">
                    //     <img
                    //       onClick={() => {
                    //         navigate(`/product/${element.id}`);
                    //       }}
                    //       src={
                    //         URLIMAGE +
                    //         element.inventories[0].variants[0].images[0]
                    //       }
                    //       alt=""
                    //       className="w-[100%] object-cover object-top  h-[100%] cursor-pointer"
                    //     />
                    //     <div className="bg-[#d2b298] flex justify-center items-center w-full h-1/6 absolute bottom-0 translate-y-full group-hover:translate-y-0 transition-transform duration-200">
                    //       <IoEyeOutline className="text-3xl " />
                    //     </div>
                    //   </div>
                    //   <div>
                    //     <p>{element.name.en}</p>
                    //     <div className="flex justify-between lg:pt-4 pt-2">
                    //       <div className="text-2xl font-semibold ">
                    //         {element.inventories[0].postPrice}.00 MAD
                    //       </div>
                    //       <div
                    //         className={
                    //           element.inventories[0].exPrice == 0
                    //             ? "hidden"
                    //             : "text-lg line-through"
                    //         }
                    //       >
                    //         {element.inventories[0].exPrice}.00 MAD
                    //       </div>
                    //     </div>
                    //     {/* <WhatsAppButton
                    //                         phoneNumber="212698974864" // Enter the phone number without '+' and country code for Morocco (212)
                    //                         message={`Hello, I am interested in purchasing ${element.title} for ${element.selPrice}.`}
                    //                         /> */}
                    //     <WhatsAppOrderButton orderPanel={orderPanel} />
                    //   </div>
                    // </div>
                    <ProductCard key={ind} element={element} />
                  )
              )}
            </div>
          </div>
          {/* Trending section */}
          <div className="flex flex-col items-center">
            <p className=" lg:pt-24 lg:pb-4 pb-2 pt-10 lg:text-5xl text-3xl font-serif">
              {" "}
              <Translator en="Trending" fr="Tendance" ar="الشائع" />
            </p>
            <img
              src={separator}
              alt=""
              className="lg:w-[fit-content] w-[60vw] "
            />
            <p className="lg:text-2xl text-lg  pb-8 lg:w-[25vw] text-center pt-4 font-normal ">
              <Translator
                en="Recently launched products"
                fr="Produits récemment lancés"
                ar="المنتجات التي تم إطلاقها مؤخرًا"
              />
            </p>
          </div>
          <div className=" flex justify-center  lg:pt-16 pb-10 ">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 lg:gap-16 gap-8 w-[80vw]">
              {product?.map((element, id) => (
                <ProductCard key={id} element={element} />
              ))}
            </div>
          </div>
          {/* last Section */}
          <div className="flex flex-col items-center pb-10">
            <img
              src={biggSeparator}
              alt=""
              className="lg:w-[fit-content] w-[80vw]  "
            />
          </div>
          <div className="  bgLastSection flex flex-col   text-white bg-[#1B1B1B]">
            <div className=" flex flex-col items-center text-center">
              <p className=" lg:pt-28 pb-4 pt-10 lg:text-4xl text-xl font-serif ">
                {" "}
                <Translator
                  en="Upcoming event"
                  fr="Événement à venir"
                  ar="حدث قادم"
                />
              </p>
              <p className="lg:text-xl text-base  lg:pb-8 lg:w-[22vw]  font-normal pt-6 ">
                <Translator
                  en="Here are some of the events that are and things to come"
                  fr="Voici quelques événements actuels et à venir"
                  ar="إليك بعض الأحداث الحالية وما هو قادم"
                />
              </p>
            </div>
            <div className="flex lg:flex-row flex-col items-center  lg:justify-center pt-8 lg:gap-8 gap-4 lg:pb-0 pb-10">
              <div className="bgleftLS flex justify-center items-center flex-col lg:gap-3 gap-2 text-nowrap lg:p-0 p-4">
                <img src={picLS} alt="" className=" h-[fit-content]" />
                <p className="text-3xl font-thin ">New Product</p>
                <p>Friday, 15 September 2021</p>
                <button
                  onClick={() =>
                    window.scrollTo({ top: 0, behavior: "smooth" })
                  }
                  className="lg:mt-4 font-medium text-sm bg-black px-4 lg:py-2 py-1 rounded-md lg:bg-opacity-25 bg-opacity-70 hover:bg-opacity-80 hover:duration-200 "
                >
                  See all
                </button>
              </div>
              <div className="bgcenterLS flex justify-center items-center flex-col lg:gap-3 gap-2 text-nowrap lg:p-0 p-4">
                <img src={picLS} alt="" className=" h-[fit-content]" />
                <p className="text-3xl font-thin ">Big sale</p>
                <p>Friday, 15 September 2021</p>
                <button
                  onClick={() => {
                    navigate("/shop");
                  }}
                  className="lg:mt-4 font-medium text-sm bg-black px-4 lg:py-2 py-1 rounded-md lg:bg-opacity-25 bg-opacity-70 hover:bg-opacity-80 hover:duration-200 "
                >
                  See all
                </button>
              </div>
              <div className="bgrightLS flex justify-center items-center  flex-col lg:gap-3 gap-2 text-nowrap lg:p-0 p-4">
                <img src={picLS} alt="" className=" h-[fit-content]" />
                <p className="text-3xl font-thin ">Blog</p>
                <p>Friday, 17 September 2021</p>
                <button
                  onClick={() => {
                    navigate("/blogs");
                  }}
                  className="lg:mt-4 font-medium text-sm bg-black px-4 lg:py-2 py-1 rounded-md lg:bg-opacity-25 bg-opacity-70 hover:bg-opacity-80 hover:duration-200 "
                >
                  See all
                </button>
              </div>
            </div>
          </div>
          <div className="flex flex-col items-center py-10">
            <img src={biggSeparator} alt="" className="w-[fit-content]" />
          </div>
        </div>
      </section>
    </>
  );
};
