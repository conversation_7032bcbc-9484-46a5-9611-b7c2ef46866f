import React from 'react';
import fbIcone from "../assets/img/fbIconeWhite.png"
import instaIcone from "../assets/img/instaIconeWhite.png"
import xIcone from "../assets/img/xIconeWhite.png"


// import './firstSection.sass'

export const Footer = () => {
    const listeCategory = ["Hijab", "Abaya", "Gamis", "Baju kurung", "Atasan", "Dress", "Out wear"]

    return (
        <>
            <section className=''>

                <div className='bg-[#2E2E2E] text-white flex lg:flex-row flex-col w-full lg:p-20 p-10'>
                    <div className='lg:w-1/3 flex flex-col lg:gap-8 gap-4 lg:pr-10'>
                        <p className='lg:text-2xl text-xl font-medium'>News</p>
                        <p className='lg:text-xl text-lg lg:pr-8 font-light'>Get detailed information from us, starting from upcoming events, promo updates & discounts by subscribing.</p>
                        <form action="" className='flex flex-col lg:gap-8 gap-4'>
                            <input type="email" className='lg:py-4 py-2 lg:px-6 px-4 outline-none placeholder:text-md text-black' placeholder='Enter your E-mail' />
                            <input type="submit" className='bg-[#9B7E5C] w-[fit-content] lg:px-10 px-6 font-medium lg:py-2 py-1 ' value="Subscribe" />
                        </form>
                    </div>
                    <div className='w-2/3 grid lg:grid-cols-3 grid-cols-1 gap-6 lg:mt-0 mt-8 lg:pl-8 '>
                        <div className='flex flex-col gap-4'>
                            <p className='lg:text-2xl text-xl font-medium'>Collections</p>
                            <div className='flex flex-col gap-2 lg:text-xl text-base font-light'>
                                {
                                    listeCategory.map((element, id) => <>
                                        <p key={id}>{element}</p>
                                    </>)
                                }
                            </div>
                        </div>
                        <div className='flex flex-col gap-4'>
                            <p className='lg:text-2xl text-xl font-medium'>Information</p>
                            <div className='flex flex-col gap-2 lg:text-xl text-base font-light'>
                                <p>Terms & Conditions</p>
                                <p>About us</p>
                                <p>Privacy</p>
                                <p>Shop address</p>
                            </div>
                        </div>
                        <div className='flex flex-col gap-4'>
                            <p className='lg:text-2xl text-xl font-medium'>Connect</p>
                            <div className='grid grid-cols-3 lg:pr-8  '>
                                <img src={fbIcone} className='' alt="" />
                                <img src={instaIcone} className='' alt="" />
                                <img src={xIcone} className='' alt="" />
                                
                            </div>
                        </div>
                        
                    </div>


                </div>
                <div className='bg-black flex justify-center gap-2 text-white font-normal  py-2 lg:text-base text-xs'>
                    <p>Copyright</p>
                    <p>&copy;</p>
                    <p>2025.</p>
                    <p>All Rights Reserved</p>


                </div>
            </section>
        </>
    );
}

