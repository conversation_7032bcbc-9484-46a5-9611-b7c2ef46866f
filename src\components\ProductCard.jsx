import React, { useContext } from "react";
import { useNavigate } from "react-router-dom";
import { MyContext } from "../utils/ContextProvider";
import { IoEyeOutline } from "react-icons/io5";
import Translator from "./Translator";

const ProductCard = ({ element }) => {
  const navigate = useNavigate();
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);
  return (
    <div dir={selectedLanguage === "ar" ? 'rtl' : "ltr"} className="flex flex-col gap-4">
      <div className="relative h-[45vh]  group overflow-hidden border-b-2 border-[#d2b298]">
        <img
          onClick={() => {
            navigate(`/product/${element.id}`);
          }}
          src={URLIMAGE + element.inventories[0].variants[0].images[0]}
          alt=""
          className="w-[100%] h-[100%] object-cover cursor-pointer"
        />
        <div className="bg-[#d2b298] flex justify-center items-center w-full h-1/6 absolute bottom-0 translate-y-full group-hover:translate-y-0 transition-transform duration-200">
          <IoEyeOutline className="text-3xl" />
        </div>
      </div>
      <div>
        <p>
          <Translator {...element.name} />{" "}
        </p>
        <div className="flex justify-between pt-4">
          <div className="text-2xl font-semibold ">
            {element.inventories[0].postPrice}.00 MAD
          </div>
          <div
            className={
              element.inventories[0].exPrice == 0
                ? "hidden"
                : "text-lg line-through"
            }
          >
            {element.inventories[0].exPrice}.00 MAD
          </div>
        </div>
        <button
          onClick={() => AddToCard(element.id)}
          className="bg-[#d2b298] px-10 mt-2 text-white py-2 text-xs font-medium"
        >
          <Translator en="Buy now" fr="Acheter maintenant" ar="اشترِ الآن" />
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
