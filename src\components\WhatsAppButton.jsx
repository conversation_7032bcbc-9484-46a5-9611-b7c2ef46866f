import React from "react";
import { IoLogoWhatsapp } from "react-icons/io5";

export const WhatsAppOrderButton = ({ orderPanel }) => {
  const handleWhatsAppClick = () => {
    const message = generateWhatsAppMessage(orderPanel);
    const phoneNumber = "212698974864"; // Replace with your WhatsApp number
    window.open(`https://wa.me/${phoneNumber}?text=${message}`, "_blank");
  };

  const generateWhatsAppMessage = (orderPanel) => {
    let message = "Hello, I would like to place an order:\n\n";
    orderPanel.products.forEach((product, index) => {
      message += `${index + 1}. ${product.name.en} X ${
        product.quantity
      } pcs @ ${
        product.inventories[0].postPrice * product.quantity
      } USD http://localhost:3000/product/${product.id} \n`;
    });
    message += `\nTotal: ${orderPanel.total} USD`;
    return encodeURIComponent(message);
  };

  return (
    <button
      onClick={handleWhatsAppClick}
      className="bg-green-500 rounded hover:bg-green-600 text-white flex items-center space-x-2 px-4 py-2"
    >
      <IoLogoWhatsapp />
      <span>Send Order</span>
    </button>
  );
};
