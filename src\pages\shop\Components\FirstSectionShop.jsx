import React, { useContext, useEffect, useState } from "react";
import miniSeparator from "../../../assets/img/miniSeparator.png";
import biggSeparator from "../../../assets/img/biggSeparator.png";
import "./shop.sass";
import { Value } from "sass";
import { useNavigate } from "react-router-dom";
import { MyContext } from "../../../utils/ContextProvider";
import { IoEyeOutline } from "react-icons/io5";
import TransText from "../../../components/TransText";
import ProductCard from "../../../components/ProductCard";

export const FirstSectionShop = () => {
  const navigate = useNavigate();
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);

  const [inputValue, setInputValue] = useState(50);
  const listeSize = ["S", "M", "L", "XL", "XXL", "XXXL"];
  const listeStatus = ["Trending", "Newest", "Best Seller"];
  const [filterStatus, setFilterStatus] = useState("All");
  const [productFilter, setProductFilter] = useState();
  useEffect(() => {
    setProductFilter(product);
  }, [product]);
  const [filterBolean, setFilterBolean] = useState(false);

  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedSizes, setSelectedSizes] = useState([]);
  const [selectedColors, setSelectedColors] = useState([]);

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const handleSizeChange = (size) => {
    setSelectedSizes((prev) =>
      prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]
    );
  };

  const handleColorChange = (color) => {
    setSelectedColors((prev) =>
      prev.includes(color) ? prev.filter((c) => c !== color) : [...prev, color]
    );
  };
  // Apply Filters Step by Step
  const filteredProducts = product
    ?.filter((prod) =>
      selectedCategories.length
        ? selectedCategories.includes(prod.category)
        : true
    )
    .filter((prod) =>
      selectedSizes.length
        ? prod.inventories.some((inventory) =>
            inventory.variants.some((variant) =>
              variant.volumes.some((volume) =>
                selectedSizes.includes(volume.size)
              )
            )
          )
        : true
    )
    .filter((prod) =>
      selectedColors.length
        ? prod.inventories.some((inventory) =>
            inventory.variants.some((variant) =>
              selectedColors.includes(variant.color)
            )
          )
        : true
    );

  const clearFilters = () => {
    setSelectedCategories([]);
    setSelectedSizes([]);
    setSelectedColors([]);
  };
  return (
    <section>
      <div className="bgshop flex justify-center items-center text-2xl font-serif text-white">
        <p>Product Catalogue</p>
      </div>
      <div className="flex flex-col items-center bg-[#F8F3E9]">
        <div className="w-[90vw] my-4   pt-10 flex gap-8 justify-between relative ">
          {/* left side : */}
          <div
            onClick={() => {
              setFilterBolean(!filterBolean);
            }}
            className="buttonFilter text-white lg:hidden md:hidden"
          >
            <p>{filterBolean ? "X" : "filter"}</p>
          </div>

          <div
            className={`lg:w-1/4 leftSideShop   lg:block ${
              filterBolean ? "block" : "hidden"
            }`}
          >
            <div className="   flex flex-col gap-8 px-6   ">
              <button
                onClick={clearFilters}
                className="mt-4 px-4 py-2 bg-orange-800 text-white rounded"
              >
                Clear Filters
              </button>
              {/* //&& Price filter  */}
              {/* <div className="pr-8">
                <p className="text-2xl font-semibold pb-4">Price</p>
                <input
                  className="w-[15vw] h-2  bg-gray-600 rounded-lg appearance-none focus:outline-none  focus:ring-gray-600 cursor-pointer"
                  onChange={(e) => setInputValue(e.target.value)}
                  type="range"
                  value={inputValue}
                  min={20}
                  max={100}
                />
              </div> */}
              <img
                className="px-4 h-[fit-content]"
                src={miniSeparator}
                alt=""
              />
              <div className="pr-8">
                <p className="text-2xl font-semibold pb-4">Colors</p>
                <div className="grid grid-cols-4 gap-4">
                  {colors?.map((color, ind) => (
                    <div
                      key={ind}
                      onClick={() => handleColorChange(color)}
                      style={{
                        backgroundColor: color,
                        border: selectedColors.includes(color)
                          ? "3px solid black"
                          : "none",
                      }}
                      className="rounded-full w-10 h-10"
                    ></div>
                  ))}
                </div>
              </div>
              <img
                className="px-4 h-[fit-content]"
                src={miniSeparator}
                alt=""
              />
              <div className="pr-8">
                <p className="text-2xl font-semibold pb-4 ">Collections</p>
                {categories?.map((category, id) => (
                  <>
                    <div className="flex justify-between pb-3">
                      <p className="font-light text-xl">{category}</p>
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category)}
                        onChange={() => handleCategoryChange(category)}
                        className="size-4 outline-double"
                      />
                    </div>
                  </>
                ))}
              </div>
              <img
                className="px-4 h-[fit-content]"
                src={miniSeparator}
                alt=""
              />
              <div className="pr-8">
                <p className="text-2xl font-semibold pb-4 ">Size</p>
                {listeSize?.map((size, id) => (
                  <div key={id} className="flex justify-between pb-3">
                    <p className="font-light text-xl">{size}</p>
                    <input
                      type="checkbox"
                      value={size}
                      checked={selectedSizes.includes(size)}
                      onChange={() => handleSizeChange(size)}
                      className="size-4 outline-double"
                    />
                  </div>
                ))}
              </div>
              <img
                className="px-4 h-[fit-content]"
                src={miniSeparator}
                alt=""
              />
            </div>
          </div>
          {/* right side : */}
          <div className=" lg:w-3/4 w-[100%] flex flex-col gap-16 lg:px-6">
            {/* <div className="lg:grid lg:grid-cols-5 flex text-nowrap lg:gap-8 gap-2 max-[480px]:overflow-scroll max-[480px]:max-w-full px-2">
              <div
                onClick={() => {
                  setFilterStatus("All");
                  setProductFilter([...product]);
                }}
                className={`text-center py-2 cursor-pointer ${
                  filterStatus != "All"
                    ? "bg-white text-black"
                    : "bg-black text-white"
                }`}
              >
                <p className="max-[480px]:w-[20vw]">All</p>
              </div>
              {listeStatus.map((element, id) => (
                <div key={id}>
                  <div
                    onClick={() => {
                      setFilterStatus(element);
                      setProductFilter(
                        product.filter(
                          (el) =>
                            el.status ==
                            element.toLowerCase().replace(/\s/g, "")
                        )
                      );
                    }}
                    className={`text-center py-2 cursor-pointer ${
                      filterStatus != element
                        ? "bg-white text-black"
                        : "bg-black text-white"
                    }`}
                  >
                    <p className="max-[480px]:w-[20vw]">{element}</p>
                  </div>
                </div>
              ))}
              <div
                onClick={() => setFilterStatus("Z-A")}
                className={`text-center py-2  cursor-pointer ${
                  filterStatus != "Z-A"
                    ? "bg-white text-black"
                    : "bg-black text-white"
                }`}
              >
                <p className="max-[480px]:w-[20vw]">Z-A</p>
              </div>
            </div> */}
            <div className="pb-10">
              <div className="grid lg:grid-cols-3 grid-cols-1 gap-8 w-full px-4">
                {filteredProducts?.map((ele, id) => (
                  <ProductCard key={id} element={ele} />
                ))}
              </div>
            </div>
          </div>
        </div>
        <img
          src={biggSeparator}
          alt=""
          className="lg:w-[fit-content] w-[80%] py-10"
        />
      </div>
    </section>
  );
};
