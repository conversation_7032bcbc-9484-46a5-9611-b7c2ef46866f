import React, { useContext } from "react";
import { MyContext } from "../../utils/ContextProvider";
import "./components/blog.sass";
import { useNavigate } from "react-router-dom";
import biggSeparator from "../../assets/img/biggSeparator.png";
// import "react-quill/dist/quill.snow.css";
export const BlogPage = () => {
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);
  // const [product, setProduct, blog, setBlog] = useContext(MyContext)
  const navigate = useNavigate();
  return (
    <section className="flex flex-col items-center bg-[#F8F3E9]">
      <div className="bgBlog flex justify-center items-center text-2xl font-serif text-white">
        <p>Blog</p>
      </div>
      <div className="w-[90vw] py-10">
        <div className="grid  lg:grid-cols-3 gap-x-8 lg:gap-y-16 gap-y-8 ">
          {blog?.map((element, id) => (
            <>
              <div
                key={id}
                onClick={() => {
                  navigate(`/blog/${element.id}`);
                }}
                className="cursor-pointer flex flex-col lg:gap-4 gap-2 bg-white/50 p-3 rounded"
              >
                <img src={URLIMAGE + element.image} alt="Blog Image" />
                <p className="lg:text-xl text-base font-serif">
                  {element.title}
                </p>
                
                <div className="ql-editor reset-tw">
                  <div
                    dangerouslySetInnerHTML={{ __html: element.description }}
                  />
                </div>
              </div>
            </>
          ))}
        </div>
      </div>
      <div className="flex w-full justify-center py-4">
        <img src={biggSeparator} alt="" className="w-[fit-content] py-10" />
      </div>
    </section>
  );
};
