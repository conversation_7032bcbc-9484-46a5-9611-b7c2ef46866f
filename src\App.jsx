
import { Route, Routes } from 'react-router-dom';
import { HomePage } from './pages/Home/home';
import { AboutPage } from './pages/About/about';
import { ErrorPage } from './pages/Error/Error';
import { MyProvider } from './utils/ContextProvider';
import { Shop } from './pages/shop/shop';
import Navbar, { Header } from './layouts/header';
import { Footer } from './layouts/footer';
import { Product } from './pages/product/product';
import { ScrollToTop } from './pages/product/scrollToTop';
import { ContactPage } from './pages/contact/contact';
import { BlogPage } from './pages/blog/blog';
import { BlogSpecificPage } from './pages/blog/components/blogSpecific';


function App() {
    return (

    <MyProvider>
        <Navbar/>
        <ScrollToTop/>
        <Routes >
            <Route path='/' element={<HomePage />} />
            <Route path='/about' element={<AboutPage />} />
            <Route path='/blogs' element={<BlogPage />} />
            <Route path='/blog/:idBlog' element={<BlogSpecificPage />} />
            <Route path='/contact' element={<ContactPage />} />
            <Route path='/shop' element={<Shop />} />
            <Route path='/product/:idProduct' element={<Product />} />
            <Route path='/*' element={<ErrorPage />} />
        </Routes >
        <Footer/>
        
        
        

    </MyProvider>
);
}
export default App;

