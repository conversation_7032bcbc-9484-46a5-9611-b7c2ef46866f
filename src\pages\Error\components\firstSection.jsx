
import './firstSection.sass'
import background from "../../../assets/img/ErrorPage.png"
import biggSeparator from "../../../assets/img/biggSeparator.png"
import { useNavigate } from 'react-router-dom';

export const FirstSectionError = () => {

    const navigate = useNavigate()


    return (
        <section className='bg-[#F8F3E9]'>
            <div className='bgError'>
                <div className='w-[100%] h-[100%] bg-[#7B7B7B] bg-opacity-80 flex flex-col items-center justify-center font-thin text-white lg:text-2xl text-lg text-center gap-8'>
                    <p className='lg:text-8xl text-5xl'>404</p>
                    <p className='font-semibold'>Oh no! Page Not Found!</p>
                    <div>
                        <p>The page is under maintenance.</p>
                        <p className='pt-2 px-10 lg:px-0'>While you're here, let's see which product is right for you! </p>
                    </div>
                    <button onClick={()=>navigate('/shop')} className='bg-[#CEA87C] w-[fit-content] mt-6 lg:px-6 px-4 lg:py-2 py-1 lg:text-xl '>View product</button>
                    
                </div>
            </div>
            <div className="flex flex-col items-center py-10 w-full">
                <img  src={biggSeparator} alt="" className=' lg:w-[fit-content]  w-[80%] ' />
            </div>
        </section>
    );
};

