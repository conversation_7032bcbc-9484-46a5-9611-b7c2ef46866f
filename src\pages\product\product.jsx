import React, { useContext, useState } from "react";
import { MyContext } from "../../utils/ContextProvider";
import { useNavigate, useParams } from "react-router-dom";

import { GoPlus } from "react-icons/go";
import { AiOutlineMinus } from "react-icons/ai";
import { IoEyeOutline } from "react-icons/io5";

import separator from "../../assets/img/Separator.png";
import biggSeparator from "../../assets/img/biggSeparator.png";

export const Product = () => {
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);

  // useState hooks must be at the top level
  let [quantity, setQuantity] = useState(1);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);
  const [selectedSize, setSelectedSize] = useState("");

  const { idProduct } = useParams();
  const navigate = useNavigate();
  const specificProduct = product?.filter((element) => element.id == idProduct);

  // Add loading check
  if (!product || !specificProduct || specificProduct.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#F8F3E9]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-xl">Loading product...</p>
        </div>
      </div>
    );
  }

  // Additional safety checks for product data structure
  const currentProduct = specificProduct[0];
  if (!currentProduct || !currentProduct.inventories || !currentProduct.inventories[0] || !currentProduct.inventories[0].variants) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#F8F3E9]">
        <div className="text-center">
          <p className="text-xl text-red-600">Product data is incomplete or corrupted.</p>
          <button
            onClick={() => navigate('/shop')}
            className="mt-4 bg-black text-white px-6 py-2 rounded"
          >
            Back to Shop
          </button>
        </div>
      </div>
    );
  }

  const productSameCategory = product
    ?.filter(
      (element) =>
        element.category == currentProduct.category &&
        element.id != idProduct
    )
    .slice(0, 4);
  const productDiffrentCategory = product
    ?.filter((element) => element.category != currentProduct.category)
    .slice(0, 4);
  console.log("ezlfnejf", currentProduct);

  // let productSameCategory4 = [...productSameCategory.slice(0, 4)]
  // console.log(productSameCategory4);

  // console.log(quantity);

  return (
    <section className="flex  justify-center bg-[#F8F3E9]">
      <div className="w-[90vw]   pt-10">
        <div className="flex ">
          {/* left side  */}
          <div className="w-2/3  justify-between  border-slate-300 border-r-2">
            <div className="grid grid-cols-2 gap-8  ">
              <div className="flex flex-col h-[fit-content] ">
                <div className="">
                  <img
                    className="w-[40vw] h-[65vh] object-contain object-center"
                    src={
                      URLIMAGE +
                      currentProduct.inventories[0].variants[selectedColorIndex].images[0]
                    }
                    alt=""
                  />
                </div>
                <div className="grid grid-cols-5 gap-6 pt-6">
                  {currentProduct?.inventories[0]?.variants
                    .slice(0, 5)
                    .map((variant, ind) => (
                      <img
                        key={ind}
                        className="cursor-pointer"
                        src={URLIMAGE + variant.images[0]}
                        alt=""
                        onClick={() => setSelectedColorIndex(ind)}
                      />
                    ))}
                </div>
              </div>
              <div className="flex flex-col justify-between gap-8 ">
                <div>
                  <p className="text-3xl font-semibold pr-2">
                    {currentProduct.name.en}
                  </p>
                  <div className="flex gap-10  pt-6">
                    <div className="text-2xl  ">
                      {currentProduct.inventories[0].postPrice}.00 MAD
                    </div>
                    <div
                      className={
                        currentProduct.inventories[0].exPrice == 0
                          ? "hidden"
                          : "text-slate-400 pt-1 line-through"
                      }
                    >
                      {currentProduct.inventories[0].exPrice}.00 MAD
                    </div>
                  </div>
                </div>
                <div>
                  <p className="text-2xl font-semibold">Colors</p>
                  <div className="grid gap-2 pt-6 grid-cols-8">
                    {currentProduct.inventories[0].variants.map(
                      (element, id) => (
                        <div
                          key={id}
                          onClick={() => setSelectedColorIndex(id)}
                          style={{ backgroundColor: `${element.color}` }}
                          className={`rounded-full w-10 h-10 cursor-pointer ${
                            selectedColorIndex === id ? 'ring-2 ring-black ring-offset-2' : ''
                          }`}
                        ></div>
                      )
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-2xl font-semibold">Size</p>
                  <div className="grid gap-2 pt-6 grid-cols-8">
                    {(() => {
                      const seenSizes = new Set();
                      return currentProduct.inventories[0].variants.flatMap(
                        (ele) =>
                          ele.volumes
                            .filter((e) => {
                              if (seenSizes.has(e.size)) return false;
                              seenSizes.add(e.size);
                              return true;
                            })
                            .map((e) => (
                              <div
                                key={e.size}
                                onClick={() => setSelectedSize(e.size)}
                                className={`flex justify-center items-center rounded-full w-10 h-10 border-2 cursor-pointer ${
                                  selectedSize === e.size
                                    ? 'border-black bg-black text-white'
                                    : 'border-black bg-white text-black hover:bg-gray-100'
                                }`}
                              >
                                {e.size}
                              </div>
                            ))
                      );
                    })()}
                  </div>
                </div>
                <div>
                  <p className="text-2xl font-semibold">Quantity</p>
                  <div className=" bg-black text-white  flex  text-lg  w-[fit-content] mt-6  px-4 py-2">
                    <button
                      onClick={() =>
                        quantity > 1 ? setQuantity(quantity - 1) : 1
                      }
                      className="lg:px-3 px-2 lg:py-1 py-1 lg:text-xl"
                    >
                      <AiOutlineMinus />
                    </button>
                    <p className="border-x-2 border-black lg:w-[4vw] w-[6vw]  flex justify-center items-center ">
                      {quantity}
                    </p>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="lg:px-3 px-2 lg:py-1 py-1 lg:text-xl"
                    >
                      <GoPlus />
                    </button>
                  </div>
                </div>
                <div className=" w-[100%]">
                  <button
                    onClick={() => {
                      if (!selectedSize) {
                        alert("Please select a size before adding to cart");
                        return;
                      }
                      AddToCard(currentProduct.id, selectedColorIndex, selectedSize, quantity);
                    }}
                    className="bg-black  w-[92%] py-2 text-white font-medium"
                  >
                    Buy Now
                  </button>
                </div>
              </div>
            </div>
            <div className="border-b-2 mt-8 mb-4 mr-9 border-slate-300"></div>
            <div>
              <p className="pb-6 text-2xl font-light">Description : </p>
              <p className="pr-6 text-lg">
                {currentProduct.description.en}{" "}
              </p>
            </div>
          </div>
          {/* right side   */}
          <div className="w-1/3 lg:px-6">
            <p className="text-3xl font-light pb-4 ">Related Product </p>
            <div className="flex flex-col gap-6 pb-6">
              {productSameCategory.map((element, id) => (
                <>
                  <div
                    key={id}
                    className="grid grid-cols-2 gap-4 w-full h-[30vh]"
                  >
                    <img
                      onClick={() => {
                        navigate(`/product/${element.id}`);
                      }}
                      src={
                        URLIMAGE + element.inventories[0].variants[0].images[0]
                      }
                      alt=""
                      className="w-full h-[30vh] cursor-pointer object-cover object-top"
                    />
                    <div className="flex flex-col h-100 justify-around ">
                      <p
                        onClick={() => {
                          navigate(`/product/${element.id}`);
                        }}
                        className="text-2xl cursor-pointer font-semibold pr-2"
                      >
                        {element.name.en}
                      </p>
                      <div className="flex gap-6">
                        <div className="text-xl  ">
                          {element.inventories[0].postPrice}.00 MAD
                        </div>
                        <div
                          className={
                            element.inventories[0].exPrice == 0
                              ? "hidden"
                              : "text-slate-400 pt-1 line-through"
                          }
                        >
                          {element.inventories[0].exPrice}.00 MAD
                        </div>
                      </div>
                      <div className="">
                        <button
                          onClick={() => {
                            navigate(`/product/${element.id}`);
                          }}
                          className="bg-black  w-[100%] py-2 text-white font-medium"
                        >
                          Detail
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              ))}
            </div>
          </div>
        </div>
        <div className="border-b-2 border-slate-200 my-10 "></div>

        {/* Best seller section */}
        <div className="flex flex-col items-center">
          <p className=" pb-4 pt-5 text-5xl font-serif">Merge Products</p>
          <img src={separator} alt="" className="w-[fit-content]" />
          <p className="text-2xl   lg:w-[25vw] text-center pt-4 pb-6 ">
            See the products you can combine
          </p>
        </div>
        <div className=" flex justify-center  pt-16 pb-10 ">
          <div className="grid lg:grid-cols-4 grid-cols-1 gap-16 w-[80vw]">
            {productDiffrentCategory.map((element, id) => (
              <div key={id} className="flex flex-col gap-4">
                <div className="relative h-[60vh]  group overflow-hidden border-b-2 border-[#d2b298]">
                  <img
                    onClick={() => {
                      navigate(`/product/${element.id}`);
                    }}
                    src={
                      URLIMAGE + element.inventories[0].variants[0].images[0]
                    }
                    alt=""
                    className="w-[100%] object-cover object-top h-[100%] cursor-pointer"
                  />
                  <div className="bg-[#d2b298] flex justify-center items-center w-full h-1/6 absolute bottom-0 translate-y-full group-hover:translate-y-0 transition-transform duration-200">
                    <IoEyeOutline className="text-3xl" />
                  </div>
                </div>
                <div>
                  <p>{element.name.en}</p>
                  <div className="flex justify-between pt-4">
                    <div className="text-2xl font-semibold ">
                      {element.inventories[0].postPrice}.00 MAD
                    </div>
                    <div
                      className={
                        element.inventories[0].exPrice == 0
                          ? "hidden"
                          : "text-lg line-through"
                      }
                    >
                      {element.inventories[0].exPrice}.00 MAD
                    </div>
                  </div>
                  <button className="bg-[#d2b298] px-10 mt-2 text-white py-2 text-xs font-medium">
                    {" "}
                    Buy now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col items-center py-10">
          <img src={biggSeparator} alt="" className="w-[fit-content] " />
        </div>

        {/* <p>{specificProduct[0][0].title} / {specificProduct[0][0].selPrice}</p>
            <img className='w-[30%]' src={specificProduct[0][0].picture} alt="" /> */}
      </div>
    </section>
  );
};
