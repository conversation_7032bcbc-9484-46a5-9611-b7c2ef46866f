import React, { useContext } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { MyContext } from "../../../utils/ContextProvider";

import separator from "../../../assets/img/Separator.png";
import biggSeparator from "../../../assets/img/biggSeparator.png";
// import 'react-quill/dist/quill.snow.css';
export const BlogSpecificPage = () => {
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);
  const { idBlog } = useParams();
  const otherBlog = blog?.filter((element) => element.id != idBlog).slice(0, 3);
  const specificBlog = blog?.filter((element) => element.id == idBlog);
  const navigate = useNavigate();
  return (
    <section className="bg-[#F8F3E9] pt-10 flex flex-col items-center">
      <div className="w-[80vw]">
        <div className="text-xl text-black">
          <p className="lg:text-5xl text-2xl font-serif text-center lg:py-10 pb-4">
            {specificBlog[0].title}
          </p>
          <img
            className="w-[100%] lg:h-[80vh]"
            src={URLIMAGE + specificBlog[0].image}
            alt=""
          />
          <p className="lg:text-xl text-lg text-start  font-serif  py-10">
           <div className="ql-editor reset-tw">
                  <div
                    dangerouslySetInnerHTML={{ __html: specificBlog[0].description }}
                  />
                </div>
          </p>
        </div>
      </div>
      <div className="flex flex-col items-center">
        <p className=" lg:pt-10 pb-4 pt-10 lg:text-5xl text-2xl font-serif">
          {" "}
          Other Articles
        </p>
        <img src={separator} alt="" className="lg:w-[fit-content] w-[60vw]" />
        <p className="lg:text-2xl  pb-8 lg:w-[30vw] text-center pt-4 font-normal">
          You can see the same article you read
        </p>
      </div>
      <div className="w-[90vw] py-10">
        <div className="grid lg:grid-cols-3 gap-x-8 lg:gap-y-16  gap-y-8  ">
          {otherBlog.map((element, id) => (
            <>
              <div
                key={id}
                onClick={() => {
                  navigate(`/blog/${element.id}`);
                }}
                className="flex flex-col lg:gap-4 gap-2"
              >
                <img src={URLIMAGE + element.image} alt="" />
                <p className="lg:text-xl text-base font-serif">
                  {element.title}
                </p>
                {/* <p className='lg:text-base text-sm '>{element.description}</p> */}
                <div className="ql-editor reset-tw">
                    <div
                      dangerouslySetInnerHTML={{ __html: element.description }}
                    />
                </div>
              </div>
            </>
          ))}
        </div>
      </div>
      <div className="flex w-full justify-center py-4">
        <img
          src={biggSeparator}
          alt=""
          className="lg:w-[fit-content] w-[80vw] py-10"
        />
      </div>
    </section>
  );
};
