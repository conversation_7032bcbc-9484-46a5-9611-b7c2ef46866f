import { useContext, useState } from "react";
import { MyContext } from "../utils/ContextProvider";

export default function LanguageDropdown() {
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
  ] = useContext(MyContext);
  const [drop, setDrop] = useState(true);
  const handleLanguageChange = (lang) => {
    // setLanguage(lang);
    setSelectedLanguage(lang);
    // Add logic for language switching here
  };
  let arrow;
  drop
    ? (arrow = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="size-5"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m19.5 8.25-7.5 7.5-7.5-7.5"
          />
        </svg>
      ))
    : (arrow = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="size-6"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m4.5 15.75 7.5-7.5 7.5 7.5"
          />
        </svg>
      ));

  return (
    <div className="relative inline-block">
      <button
        className="p-2 rounded-xl  flex items-center"
        onClick={(e) => {
          e.currentTarget.nextSibling.classList.toggle("hidden");
          setDrop(!drop);
        }}
      >
        <span className="capitalize mr-2">{selectedLanguage}</span>
        {arrow}
      </button>
      <div className="absolute top-full left-0 mt-1 w-24 bg-white border border-gray-300 rounded-xl hidden">
        <div
          className="p-2 cursor-pointer hover:bg-gray-100"
          onClick={() => handleLanguageChange("en")}
        >
          English
        </div>
        <div
          className="p-2 cursor-pointer hover:bg-gray-100"
          onClick={() => handleLanguageChange("fr")}
        >
          Français
        </div>
        <div
          className="p-2 cursor-pointer hover:bg-gray-100"
          onClick={() => handleLanguageChange("ar")}
        >
          العربية
        </div>
      </div>
    </div>
  );
}
