import React from 'react';
import biggSeparator from '../../assets/img/biggSeparator.png'


import './contact.sass'

export const ContactPage = () => {
    return (
        <section className='bg-[#F8F3E9]'>
            <div className="flex lg:flex-row flex-col w-full lg:h-[90vh]">
                <div className='w-[100%] bgpictureLeft flex justify-center items-center lg-py-0 py-16'>
                    <div className='w-[70%] lg:h-[80%] bg-gray-500 bg-opacity-40 lg:p-20 p-10 text-white'>
                        <p className='text-2xl font-serif'>Hijab Store</p>
                        <div className='pt-10'>
                            <p className='text-3xl font-serif'>Contact</p>
                            <p className='font-thin text-lg pr-6 pt-3'>We really appreciate the time and input you give us, help us to continue to provide the best service.</p>
                        </div>
                    </div>
                </div>
                <div className='w-[100%] flex justify-center items-center'>
                    <div className='lg:w-[60%]  w-[80%] flex flex-col gap-10'>
                        <p className='lg:text-3xl text-2xl lg:text-start text-center font-serif lg:pt-0 pt-6'>What can we help?</p>
                        <form action="">
                            <div className='flex flex-col gap-4'>
                                <input type="text" className='p-4 outline-none' placeholder='Name' />
                                <input type="mail" className='p-4 outline-none' placeholder='E-mail' />
                                {/* <input type="text" /> */}
                                <textarea name="" id="" className='p-4 outline-none h-40' placeholder='Message'></textarea>
                                <button className='bg-[#9B7E5C] lg:mt-6 w-[fit-content] px-10 py-2 font-medium text-white'>Submit</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div className="flex w-full justify-center py-4">
                <img src={biggSeparator} alt="" className='lg:w-[fit-content] w-[80%] py-10' />
            </div>



        </section>
    );
};

