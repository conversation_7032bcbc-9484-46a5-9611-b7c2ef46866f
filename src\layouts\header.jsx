import React, { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { SlBasket } from "react-icons/sl";
import { Sidebar } from "./components/sidebare";
import { MyContext } from "../utils/ContextProvider";

import logo from "../assets/img/hSIPicture.png";
import LanguageDropdown from "../components/LanguageDrpdown";
import Translator from "../components/Translator";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [
    product,
    setProduct,
    blog,
    setBlog,
    url,
    URLIMAGE,
    sidebareBoolean,
    setSidebareBoolean,
    basket,
    setBasket,
    AddToCard,
    categories,
    colors,
    selectedLanguage,
    setSelectedLanguage,
    removeFromCart,
  ] = useContext(MyContext);

  const navigate = useNavigate();

  return (
    <div
      dir={`${selectedLanguage === "ar" ? "rtl" : "ltr"}`}
      className="h-[fit-content]  z-50 sticky top-0 border-b-2 border-[#CEA87C]"
    >
      <div className="antialiased bg-[#F8F3E9]">
        <div className="w-full text-gray-700 ">
          <div className="flex flex-col max-w-screen-xl px-4 mx-auto md:items-center md:justify-between md:flex-row md:px-6 lg:px-8 ">
            <div className="flex flex-row items-center justify-between  ">
              <img
                onClick={() => navigate("/")}
                className="w-20 p-1"
                src={logo}
                alt=""
              />
              <div className="flex gap-2">
                <div className="border-r-2 pr-2 border-[#CEA87C] lg:hidden md:hidden block">
                  <div
                    onClick={() => {
                      setSidebareBoolean(!sidebareBoolean);
                    }}
                    className="relative cursor-pointer"
                  >
                    <p className="absolute bg-yellow-500 rounded-full px-1 text-xs top-0 right-0  ">
                      {basket.length}
                    </p>
                    <SlBasket className="text-3xl px-1" />
                    <div>
                      <Sidebar />
                    </div>
                  </div>
                </div>
                <button
                  className="rounded-lg md:hidden focus:outline-none focus:shadow-outline "
                  onClick={() => setIsOpen(!isOpen)}
                >
                  {isOpen ? (
                    <svg
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      className="w-6 h-6"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  ) : (
                    <svg
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      className="w-6 h-6"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  )}
                </button>
              </div>
            </div>
            {/* Navigation Links */}
            <nav
              className={`${
                isOpen ? "flex" : "hidden"
              } flex-col flex-grow pb-4 md:pb-0 md:flex md:justify-center md:flex-row`}
            >
              <p
                onClick={() => navigate("/")}
                className="px-4 py-2 mt-2 text-sm font-semibold bg-transparent rounded-lg cursor-pointer hover:text-gray-900 focus:text-gray-900 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none focus:shadow-outline"
              >
                <Translator en="Home" fr="Acceuil" ar="الرئيسية" />
              </p>
              <p
                onClick={() => navigate("/shop")}
                className="px-4 py-2 mt-2 text-sm font-semibold bg-transparent rounded-lg cursor-pointer hover:text-gray-900 focus:text-gray-900 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none focus:shadow-outline"
              >
                <Translator en="Shop" fr="Boutique" ar="المتجر" />
              </p>
              <p
                onClick={() => navigate("/contact")}
                className="px-4 py-2 mt-2 text-sm font-semibold bg-transparent rounded-lg cursor-pointer hover:text-gray-900 focus:text-gray-900 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none focus:shadow-outline"
              >
                <Translator en="Contact" fr="Contact" ar="اتصال" />
              </p>
              <p
                onClick={() => navigate("/about")}
                className="px-4 py-2 mt-2 text-sm font-semibold bg-transparent rounded-lg cursor-pointer hover:text-gray-900 focus:text-gray-900 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none focus:shadow-outline"
              >
                <Translator en="About" fr="À propos" ar="حول" />
              </p>
              <p
                onClick={() => navigate("/blogs")}
                className="px-4 py-2 mt-2 text-sm font-semibold bg-transparent rounded-lg cursor-pointer hover:text-gray-900 focus:text-gray-900 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none focus:shadow-outline"
              >
                <Translator en="Blog" fr="Blog" ar="مدونة" />
              </p>
            </nav>
            <LanguageDropdown />
            <div className="hidden md:block lg:block">
              <div
                onClick={() => {
                  setSidebareBoolean(!sidebareBoolean);
                }}
                className="relative cursor-pointer"
              >
                <p className="absolute bg-yellow-500 rounded-full px-1 text-xs top-0 right-0  ">
                  {basket.length}
                </p>
                <SlBasket className="text-3xl px-1" />
                <div>
                  <Sidebar />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
