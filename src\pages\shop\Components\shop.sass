.bgshop
    background-image: url('../../../assets/img/shopbg.png')
    background-size: cover
    background-repeat: no-repeat
    height: 15vh
    width: 100%

input[type="checkbox"]
    appearance: none
    background-color: #fff
    margin: 0
    font: inherit
    color: #d2b298
    width: 1.15em
    height: 1.15em
    border: 0.15em solid #d2b298
    border-radius: 10%
    transform: translateY(-0.075em)
    display: grid
    place-content: center

input[type="checkbox"]::before
    content: ""
    width: 0.65em
    height: 0.65em
    border-radius: 10%
    transform: scale(0)
    transition: 120ms transform ease-in-out
    box-shadow: inset 1em 1em #cf8448

input[type="checkbox"]:checked::before
    transform: scale(1)

@media (max-width: 480px)
    .buttonFilter
        width: fit-content
        padding-inline: 10px
        height: fit-content
        background: #000000
        z-index: 20
        border-radius: 3px
    .leftSideShop
        width: 90%
        background-color: #ebebedf1
        z-index: 10
        padding-inline: 10px
        padding-block: 20px
        max-height: 70%
        overflow-y: auto
        position: fixed
        top: 26vh
        border-radius: 5px
        -webkit-overflow-scrolling: auto
        background: linear-gradient(-45deg, #b2b8b8, #d4b39b)
        animation-duration: 500ms

        // display: none
