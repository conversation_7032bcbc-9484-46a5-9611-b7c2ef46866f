import React, { useContext, useState } from 'react';
import { MyContext } from '../../utils/ContextProvider';
import './sidebare.sass'

export const SidebarFilter = () => {
    const [product, setProduct, blog, setBlog, url, URLIMAGE, sidebareBoolean, setSidebareBoolean, basket, setBasket] = useContext(MyContext);
    const toggleSidebarFilter = () => {
        setBooleanFilter(!booleanFilter);
    };

    return (
        <div className="flex relative font-serif">
            {/* Sidebar */}
            <div
                className={`fixed top-0 right-0  h-full bg-black text-white p-4 transition-transform lg:w-[25vw] w-[80vw] bg-opacity-80  ${booleanFilter ? 'translate-x-0' : 'translate-x-full'
                    }`}
            >
                <h2 className="text-2xl font-bold mb-4">Panier : </h2>
                <hr />
                <div className='flex flex-col gap-1 mt-2 overflow-y-auto lg:max-h-[80vh] lg:h-[80vh] max-h-[78vh] h-[78vh] lg:text-lg text-sm my-4 custom-scrollbar pr-2'>
                    <p>content</p>
                </div>
                <hr />
                <div>
                    <button onClick={()=>{alert('nqi rak tqdity')}} className='bg-white text-black  lg:py-2 py-1 px-6  rounded-md absolute lg:bottom-2 bottom-3 left-4'>CheckOut</button>
                    <div onClick={()=>{alert('nqi rak tqdity')}} className='bg-white text-black  lg:py-2 py-1 px-2   rounded-md absolute lg:bottom-2 bottom-3 right-4'>Total : $ 180.00</div>
                </div>
            </div>
            {/* Show/Hide Button */}
            <button
                onClick={toggleSidebarFilter}
                className="fixed top-2 right-4 z-10  text-white p-2  focus:outline-none"
            >
                {booleanFilter ? <span className="text-xl font-bold">X</span>  : ""}
            </button>
        </div>
    );
};


